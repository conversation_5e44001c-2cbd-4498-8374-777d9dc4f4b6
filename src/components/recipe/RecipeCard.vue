<template>
    <div class="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 overflow-hidden fresh-card fresh-shadow border-0 bg-white rounded-lg"
        @click="onClick">
        <!-- 图片区域 -->
        <div class="aspect-video relative overflow-hidden">
            <img :src="recipe.image" :alt="recipe.title"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                @error="handleImageError" />
            <!-- 难度标签 -->
            <div class="absolute top-3 right-3">
                <span :class="getDifficultyColor(recipe.difficulty)"
                    class="px-2 py-1 rounded-full text-xs font-medium shadow-sm border">
                    {{ recipe.difficulty }}
                </span>
            </div>
            <!-- 悬停遮罩 -->
            <div
                class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        <!-- 内容区域 -->
        <div class="p-5">
            <div class="space-y-3">
                <!-- 标题 -->
                <h3 class="text-lg font-semibold text-gray-800 line-clamp-2">
                    {{ recipe.title }}
                </h3>

                <!-- 描述 -->
                <p class="text-gray-600 text-sm line-clamp-2">
                    {{ recipe.description }}
                </p>

                <!-- 底部信息 -->
                <div class="flex items-center justify-between pt-2">
                    <div class="flex items-center gap-4 text-sm text-gray-600">
                        <!-- 烹饪时间 -->
                        <div class="flex items-center gap-1">
                            <Clock class="w-4 h-4 text-emerald-600" />
                            <span>{{ recipe.cookTime }}分钟</span>
                        </div>
                        <!-- 人数 -->
                        <div class="flex items-center gap-1">
                            <Users class="w-4 h-4 text-emerald-600" />
                            <span>{{ recipe.servings }}人份</span>
                        </div>
                    </div>

                    <!-- 分类标签 -->
                    <span
                        class="px-2 py-1 bg-emerald-50 text-emerald-700 border-emerald-200 border rounded-full text-xs font-medium">
                        {{ recipe.category }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Clock, Users } from 'lucide-vue-next'
import type { RecipeCardProps } from '@/types'

defineProps<RecipeCardProps>()

const getDifficultyColor = (difficulty: string): string => {
    switch (difficulty) {
        case '简单':
            return 'bg-emerald-50 text-emerald-700 border-emerald-200'
        case '中等':
            return 'bg-amber-50 text-amber-700 border-amber-200'
        case '困难':
            return 'bg-orange-50 text-orange-700 border-orange-200'
        default:
            return 'bg-gray-50 text-gray-700 border-gray-200'
    }
}

const handleImageError = (event: Event) => {
    const target = event.target as HTMLImageElement
    target.src = 'https://images.unsplash.com/photo-1546548970-71785318a17b?w=600&h=400&fit=crop'
}
</script>

<style scoped>
.fresh-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.fresh-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>