<template>
  <div class="space-y-6">
    <!-- 返回按钮 -->
    <div class="flex items-center gap-4">
      <button
        @click="onBack"
        class="flex items-center gap-2 px-4 py-2 text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50 rounded-lg transition-colors"
      >
        <ArrowLeft class="w-4 h-4" />
        返回列表
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 左侧：图片和基本信息 -->
      <div class="space-y-6">
        <!-- 菜谱图片 -->
        <div class="aspect-video rounded-lg overflow-hidden fresh-shadow">
          <img
            :src="recipe.image"
            :alt="recipe.title"
            class="w-full h-full object-cover"
            @error="handleImageError"
          />
        </div>

        <!-- 基本信息 -->
        <div class="space-y-4">
          <div class="space-y-2">
            <h1 class="text-3xl font-bold text-gray-800">{{ recipe.title }}</h1>
            <p class="text-gray-600 text-lg">{{ recipe.description }}</p>
          </div>

          <!-- 标签信息 -->
          <div class="flex flex-wrap gap-2">
            <span :class="getDifficultyColor(recipe.difficulty)" class="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border shadow-sm">
              <ChefHat class="w-3 h-3" />
              {{ recipe.difficulty }}
            </span>
            <span class="inline-flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 border-blue-200 border rounded-full text-sm font-medium">
              <Clock class="w-3 h-3" />
              {{ recipe.cookTime }}分钟
            </span>
            <span class="inline-flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 border-blue-200 border rounded-full text-sm font-medium">
              <Users class="w-3 h-3" />
              {{ recipe.servings }}人份
            </span>
            <span class="px-3 py-1 border-emerald-200 text-emerald-700 hover:bg-emerald-50 border rounded-full text-sm font-medium">
              {{ recipe.category }}
            </span>
          </div>
        </div>
      </div>

      <!-- 右侧：食材和步骤 -->
      <div class="space-y-6">
        <!-- 所需材料 -->
        <div class="fresh-card border-0 fresh-shadow rounded-lg overflow-hidden">
          <div class="bg-gradient-to-r from-emerald-50 to-green-50 px-6 py-4">
            <h2 class="text-xl font-semibold text-emerald-800 flex items-center gap-2">
              <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
              所需材料
            </h2>
          </div>
          <div class="p-6">
            <ul class="space-y-3">
              <li
                v-for="(ingredient, index) in recipe.ingredients"
                :key="index"
                class="flex items-center gap-3 p-2 rounded-lg hover:bg-emerald-50 transition-colors"
              >
                <div class="w-2 h-2 bg-emerald-400 rounded-full flex-shrink-0"></div>
                <span class="text-gray-700">{{ ingredient }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- 制作步骤 -->
        <div class="fresh-card border-0 fresh-shadow rounded-lg overflow-hidden">
          <div class="bg-gradient-to-r from-blue-50 to-sky-50 px-6 py-4">
            <h2 class="text-xl font-semibold text-blue-800 flex items-center gap-2">
              <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
              制作步骤
            </h2>
          </div>
          <div class="p-6">
            <div class="space-y-6">
              <div
                v-for="(step, index) in recipe.steps"
                :key="index"
                class="flex gap-4"
              >
                <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-emerald-500 to-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium shadow-sm">
                  {{ index + 1 }}
                </div>
                <div class="flex-1 pt-1">
                  <p class="text-gray-700 leading-relaxed">{{ step }}</p>
                  <div v-if="index < recipe.steps.length - 1" class="mt-4 h-px bg-emerald-100"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, Clock, Users, ChefHat } from 'lucide-vue-next'
import type { RecipeDetailProps } from '@/types'

const props = defineProps<RecipeDetailProps>()

const getDifficultyColor = (difficulty: string): string => {
  switch (difficulty) {
    case '简单':
      return 'bg-emerald-50 text-emerald-700 border-emerald-200'
    case '中等':
      return 'bg-amber-50 text-amber-700 border-amber-200'
    case '困难':
      return 'bg-orange-50 text-orange-700 border-orange-200'
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200'
  }
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = 'https://images.unsplash.com/photo-1546548970-71785318a17b?w=600&h=400&fit=crop'
}
</script>

<style scoped>
.fresh-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.fresh-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
