<template>
  <div class="space-y-8">
    <!-- 头部区域 -->
    <div class="text-center space-y-4 py-8 fresh-gradient rounded-2xl">
      <div class="flex items-center justify-center gap-3">
        <div class="p-3 bg-white rounded-full shadow-lg">
          <Leaf class="w-8 h-8 text-emerald-600" />
        </div>
        <h1 class="text-3xl font-bold text-emerald-800">我的菜谱</h1>
      </div>
      <p class="text-emerald-700 max-w-md mx-auto">
        收藏你喜欢的美食，随时查看制作方法，享受烹饪的乐趣
      </p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="flex flex-col sm:flex-row gap-4 p-6 bg-white rounded-xl fresh-shadow border border-emerald-100">
      <!-- 搜索框 -->
      <div class="relative flex-1">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-500 w-4 h-4" />
        <input
          v-model="searchTerm"
          type="text"
          placeholder="搜索菜谱..."
          class="w-full pl-10 pr-4 py-2 border border-emerald-200 rounded-lg focus:border-emerald-400 focus:ring-2 focus:ring-emerald-200 bg-emerald-50/50 outline-none transition-colors"
        />
      </div>
      
      <!-- 分类选择 -->
      <select
        v-model="selectedCategory"
        class="w-full sm:w-48 px-4 py-2 border border-emerald-200 rounded-lg focus:border-emerald-400 focus:ring-2 focus:ring-emerald-200 bg-emerald-50/50 outline-none transition-colors"
      >
        <option value="all">所有分类</option>
        <option v-for="category in categories.slice(1)" :key="category" :value="category">
          {{ category }}
        </option>
      </select>
    </div>

    <!-- 菜谱网格 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <RecipeCard
        v-for="recipe in filteredRecipes"
        :key="recipe.id"
        :recipe="recipe"
        @click="() => onRecipeSelect(recipe)"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="filteredRecipes.length === 0" class="text-center py-16">
      <div class="inline-flex items-center justify-center w-16 h-16 bg-emerald-50 rounded-full mb-4">
        <Search class="w-6 h-6 text-emerald-400" />
      </div>
      <p class="text-emerald-600 text-lg font-medium">没有找到匹配的菜谱</p>
      <p class="text-sm text-emerald-500 mt-1">试试其他关键词或分类</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Search, Leaf } from 'lucide-vue-next'
import RecipeCard from './RecipeCard.vue'
import type { RecipeListProps } from '@/types'

const props = defineProps<RecipeListProps>()

const searchTerm = ref('')
const selectedCategory = ref('all')

// 获取所有分类
const categories = computed(() => {
  return ['all', ...Array.from(new Set(props.recipes.map(r => r.category)))]
})

// 过滤后的菜谱
const filteredRecipes = computed(() => {
  return props.recipes.filter(recipe => {
    const matchesSearch = recipe.title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                         recipe.description.toLowerCase().includes(searchTerm.value.toLowerCase())
    const matchesCategory = selectedCategory.value === 'all' || recipe.category === selectedCategory.value
    return matchesSearch && matchesCategory
  })
})
</script>

<style scoped>
.fresh-gradient {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
}

.fresh-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
