import { createRouter, createWebHistory } from 'vue-router'
import { HomeView, RecipeView } from '@/views'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '乐享厨房 - 发现美食的无限可能',
      },
    },
    {
      path: '/recipes',
      name: 'recipes',
      component: RecipeView,
      meta: {
        title: '我的菜谱 - 乐享厨房',
      },
    },
    // 重定向处理
    {
      path: '/:pathMatch(.*)*',
      redirect: '/',
    },
  ],
})

// 路由守卫 - 设置页面标题
router.beforeEach((to) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
})

export default router
