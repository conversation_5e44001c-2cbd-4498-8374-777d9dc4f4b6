/**
 * 菜谱接口定义
 */
export interface Recipe {
  /** 菜谱唯一标识 */
  id: number
  /** 菜谱名称 */
  title: string
  /** 菜谱描述 */
  description: string
  /** 菜谱图片URL */
  image: string
  /** 烹饪时间（分钟） */
  cookTime: number
  /** 适合人数 */
  servings: number
  /** 难度等级 */
  difficulty: DifficultyLevel
  /** 菜谱分类 */
  category: string
  /** 所需食材列表 */
  ingredients: string[]
  /** 制作步骤列表 */
  steps: string[]
}

/**
 * 难度等级枚举
 */
export type DifficultyLevel = '简单' | '中等' | '困难'

/**
 * 菜谱分类枚举
 */
export type RecipeCategory = '荤菜' | '素菜' | '面食' | '主食' | '川菜' | '汤品' | '甜品' | '小食'

/**
 * 搜索过滤条件
 */
export interface RecipeFilter {
  /** 搜索关键词 */
  searchTerm?: string
  /** 分类筛选 */
  category?: string
  /** 难度筛选 */
  difficulty?: DifficultyLevel
  /** 烹饪时间范围筛选 */
  cookTimeRange?: {
    min: number
    max: number
  }
}

/**
 * 菜谱列表组件属性
 */
export interface RecipeListProps {
  recipes: Recipe[]
  onRecipeSelect: (recipe: Recipe) => void
}

/**
 * 菜谱卡片组件属性
 */
export interface RecipeCardProps {
  recipe: Recipe
  onClick: () => void
}

/**
 * 菜谱详情组件属性
 */
export interface RecipeDetailProps {
  recipe: Recipe
  onBack: () => void
}
