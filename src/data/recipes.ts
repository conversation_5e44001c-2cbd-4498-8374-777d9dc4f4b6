import type { Recipe } from '@/types'

/**
 * 菜谱模拟数据
 */
export const mockRecipes: Recipe[] = [
  {
    id: 1,
    title: '经典红烧肉',
    description: '软糯香甜的传统家常菜，肥而不腻，入口即化',
    image: 'https://images.unsplash.com/photo-1626804475297-41608ea09aeb?w=600&h=400&fit=crop',
    cookTime: 90,
    servings: 4,
    difficulty: '中等',
    category: '荤菜',
    ingredients: [
      '五花肉 500g',
      '生抽 3勺',
      '老抽 1勺',
      '料酒 2勺',
      '冰糖 30g',
      '生姜 3片',
      '大葱 2段',
      '八角 2个'
    ],
    steps: [
      '五花肉洗净切成2cm见方的块，用开水焯烫去血沫',
      '锅中放少许油，下入五花肉块小火煸炒至表面微黄',
      '加入生姜片、大葱段、八角炒香',
      '倒入料酒、生抽、老抽炒匀上色',
      '加入适量开水没过肉块，大火烧开后转小火炖煮60分钟',
      '最后大火收汁，撒上葱花即可'
    ]
  },
  {
    id: 2,
    title: '番茄鸡蛋面',
    description: '简单快手的经典面条，酸甜开胃，营养丰富',
    image: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=600&h=400&fit=crop',
    cookTime: 15,
    servings: 1,
    difficulty: '简单',
    category: '面食',
    ingredients: [
      '挂面 100g',
      '鸡蛋 2个',
      '番茄 2个',
      '葱花 适量',
      '盐 适量',
      '糖 1勺',
      '生抽 1勺'
    ],
    steps: [
      '番茄划十字刀用开水烫去皮，切块备用',
      '鸡蛋打散炒熟盛起',
      '锅中放油炒番茄出汁',
      '加入炒蛋翻炒均匀，调味',
      '另起锅煮面条至8分熟',
      '将面条加入番茄鸡蛋中拌匀即可'
    ]
  },
  {
    id: 3,
    title: '蒜蓉西兰花',
    description: '清爽的素菜，保持蔬菜的营养和脆嫩口感',
    image: 'https://images.unsplash.com/photo-1628773822503-930a7eaecf80?w=600&h=400&fit=crop',
    cookTime: 10,
    servings: 2,
    difficulty: '简单',
    category: '素菜',
    ingredients: [
      '西兰花 300g',
      '大蒜 4瓣',
      '盐 适量',
      '生抽 1勺',
      '香油 几滴'
    ],
    steps: [
      '西兰花洗净切小朵，蒜切蓉',
      '锅中水开后放入西兰花焯水2分钟',
      '捞出过凉水保持翠绿色',
      '热锅下油爆香蒜蓉',
      '倒入西兰花大火炒匀',
      '调味即可出锅'
    ]
  },
  {
    id: 4,
    title: '可乐鸡翅',
    description: '孩子们最爱的甜口鸡翅，简单易做，香甜诱人',
    image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=600&h=400&fit=crop',
    cookTime: 30,
    servings: 3,
    difficulty: '简单',
    category: '荤菜',
    ingredients: [
      '鸡翅 8个',
      '可乐 300ml',
      '生抽 2勺',
      '老抽 1勺',
      '料酒 1勺',
      '生姜 2片',
      '葱段 适量'
    ],
    steps: [
      '鸡翅洗净在表面划几刀便于入味',
      '热锅下油将鸡翅煎至两面金黄',
      '加入生姜片爆香',
      '倒入料酒、生抽、老抽炒匀',
      '倒入可乐没过鸡翅，大火烧开',
      '转小火炖煮15分钟，大火收汁即可'
    ]
  },
  {
    id: 5,
    title: '蛋炒饭',
    description: '家常快手饭，米粒分明，蛋香浓郁',
    image: 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=600&h=400&fit=crop',
    cookTime: 10,
    servings: 1,
    difficulty: '简单',
    category: '主食',
    ingredients: [
      '米饭 200g',
      '鸡蛋 2个',
      '火腿肠 50g',
      '青豆 30g',
      '胡萝卜丁 30g',
      '葱花 适量',
      '盐 适量',
      '生抽 1勺'
    ],
    steps: [
      '米饭用勺子压散，鸡蛋打散',
      '热锅下油炒鸡蛋盛起',
      '锅中放油下配菜炒熟',
      '倒入米饭炒散',
      '加入炒蛋翻炒均匀',
      '调味撒葱花即可'
    ]
  },
  {
    id: 6,
    title: '麻婆豆腐',
    description: '四川经典菜品，麻辣鲜香，嫩滑入味',
    image: 'https://images.unsplash.com/photo-1586190848861-99aa4a171e90?w=600&h=400&fit=crop',
    cookTime: 20,
    servings: 3,
    difficulty: '中等',
    category: '川菜',
    ingredients: [
      '嫩豆腐 400g',
      '牛肉末 100g',
      '豆瓣酱 2勺',
      '花椒粉 适量',
      '大蒜 3瓣',
      '生姜 1块',
      '葱花 适量',
      '生抽 1勺',
      '淀粉 1勺'
    ],
    steps: [
      '豆腐切块用盐水焯烫去豆腥味',
      '热锅下油炒牛肉末至变色',
      '加入豆瓣酱炒出红油',
      '下蒜末姜末爆香',
      '加适量水烧开，放入豆腐',
      '小火炖煮5分钟，水淀粉勾芡',
      '撒花椒粉和葱花即可'
    ]
  }
]

/**
 * 获取所有菜谱分类
 */
export const getRecipeCategories = (): string[] => {
  const categories = Array.from(new Set(mockRecipes.map(recipe => recipe.category)))
  return ['全部', ...categories]
}

/**
 * 根据分类筛选菜谱
 */
export const getRecipesByCategory = (category: string): Recipe[] => {
  if (category === '全部') {
    return mockRecipes
  }
  return mockRecipes.filter(recipe => recipe.category === category)
}

/**
 * 根据ID获取菜谱
 */
export const getRecipeById = (id: number): Recipe | undefined => {
  return mockRecipes.find(recipe => recipe.id === id)
}

/**
 * 搜索菜谱
 */
export const searchRecipes = (searchTerm: string): Recipe[] => {
  if (!searchTerm.trim()) {
    return mockRecipes
  }
  
  const term = searchTerm.toLowerCase()
  return mockRecipes.filter(recipe => 
    recipe.title.toLowerCase().includes(term) ||
    recipe.description.toLowerCase().includes(term) ||
    recipe.ingredients.some(ingredient => ingredient.toLowerCase().includes(term))
  )
}
