<script setup lang="ts">
import { onMounted } from 'vue'
import { useRecipeStore } from '@/stores'

const recipeStore = useRecipeStore()

// 应用初始化
onMounted(async () => {
  await recipeStore.initialize()
})
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

#app {
  min-height: 100vh;
}

/* 工具类 */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 响应式断点 */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}
</style>
