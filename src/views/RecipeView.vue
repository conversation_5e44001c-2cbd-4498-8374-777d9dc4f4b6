<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
      <!-- 菜谱详情页面 -->
      <RecipeDetail
        v-if="selectedRecipe"
        :recipe="selectedRecipe"
        @back="handleBack"
      />
      
      <!-- 菜谱列表页面 -->
      <RecipeList
        v-else
        :recipes="recipes"
        @recipe-select="handleRecipeSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { RecipeList, RecipeDetail } from '@/components/recipe'
import { mockRecipes } from '@/data'
import type { Recipe } from '@/types'

// 响应式数据
const recipes = ref<Recipe[]>([])
const selectedRecipe = ref<Recipe | null>(null)

// 处理菜谱选择
const handleRecipeSelect = (recipe: Recipe) => {
  selectedRecipe.value = recipe
}

// 处理返回列表
const handleBack = () => {
  selectedRecipe.value = null
}

// 组件挂载时加载数据
onMounted(() => {
  recipes.value = mockRecipes
})
</script>

<style scoped>
.container {
  max-width: 1280px;
}
</style>
