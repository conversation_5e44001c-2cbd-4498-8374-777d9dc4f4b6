<template>
  <div class="min-h-screen bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-sm border-b border-emerald-100 sticky top-0 z-50">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-emerald-100 rounded-lg">
              <ChefHat class="w-6 h-6 text-emerald-600" />
            </div>
            <h1 class="text-xl font-bold text-emerald-800">乐享厨房</h1>
          </div>
          
          <div class="flex items-center gap-4">
            <router-link
              to="/recipes"
              class="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
            >
              查看菜谱
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-16">
      <!-- 英雄区域 -->
      <div class="text-center space-y-8 mb-16">
        <div class="space-y-4">
          <h1 class="text-5xl font-bold text-gray-800 leading-tight">
            发现美食的
            <span class="text-emerald-600">无限可能</span>
          </h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            收藏你喜欢的菜谱，学习烹饪技巧，与家人朋友分享美食的快乐时光
          </p>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/recipes"
            class="px-8 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-lg font-medium"
          >
            开始探索
          </router-link>
          <button class="px-8 py-3 border-2 border-emerald-600 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors text-lg font-medium">
            了解更多
          </button>
        </div>
      </div>

      <!-- 特色功能 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        <div class="text-center space-y-4 p-6 bg-white rounded-xl shadow-sm">
          <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto">
            <BookOpen class="w-8 h-8 text-emerald-600" />
          </div>
          <h3 class="text-xl font-semibold text-gray-800">丰富菜谱</h3>
          <p class="text-gray-600">精选各类美食菜谱，从家常菜到特色料理，应有尽有</p>
        </div>
        
        <div class="text-center space-y-4 p-6 bg-white rounded-xl shadow-sm">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
            <Clock class="w-8 h-8 text-blue-600" />
          </div>
          <h3 class="text-xl font-semibold text-gray-800">快速搜索</h3>
          <p class="text-gray-600">智能搜索功能，快速找到你想要的菜谱和烹饪方法</p>
        </div>
        
        <div class="text-center space-y-4 p-6 bg-white rounded-xl shadow-sm">
          <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
            <Heart class="w-8 h-8 text-orange-600" />
          </div>
          <h3 class="text-xl font-semibold text-gray-800">个人收藏</h3>
          <p class="text-gray-600">收藏喜欢的菜谱，建立属于你的私人美食库</p>
        </div>
      </div>

      <!-- 热门菜谱预览 -->
      <div class="space-y-8">
        <div class="text-center">
          <h2 class="text-3xl font-bold text-gray-800 mb-4">热门菜谱</h2>
          <p class="text-gray-600">最受欢迎的美食推荐</p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="recipe in featuredRecipes"
            :key="recipe.id"
            class="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
            @click="goToRecipes"
          >
            <div class="aspect-video relative">
              <img
                :src="recipe.image"
                :alt="recipe.title"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-3 right-3">
                <span class="px-2 py-1 bg-white/90 text-emerald-700 rounded-full text-xs font-medium">
                  {{ recipe.difficulty }}
                </span>
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-semibold text-gray-800 mb-2">{{ recipe.title }}</h3>
              <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ recipe.description }}</p>
              <div class="flex items-center justify-between text-sm text-gray-500">
                <span class="flex items-center gap-1">
                  <Clock class="w-4 h-4" />
                  {{ recipe.cookTime }}分钟
                </span>
                <span class="flex items-center gap-1">
                  <Users class="w-4 h-4" />
                  {{ recipe.servings }}人份
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="text-center">
          <router-link
            to="/recipes"
            class="inline-flex items-center gap-2 px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
          >
            查看更多菜谱
            <ArrowRight class="w-4 h-4" />
          </router-link>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-emerald-100 mt-16">
      <div class="container mx-auto px-4 py-8">
        <div class="text-center text-gray-600">
          <p>&copy; 2024 乐享厨房. 让烹饪变得更简单.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ChefHat, BookOpen, Clock, Heart, Users, ArrowRight } from 'lucide-vue-next'
import { mockRecipes } from '@/data'

const router = useRouter()

// 精选菜谱（前3个）
const featuredRecipes = computed(() => mockRecipes.slice(0, 3))

// 跳转到菜谱页面
const goToRecipes = () => {
  router.push('/recipes')
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.container {
  max-width: 1280px;
}
</style>
