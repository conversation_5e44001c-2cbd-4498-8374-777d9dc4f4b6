import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { mockRecipes, getRecipeCategories, getRecipesByCategory, getRecipeById, searchRecipes } from '@/data'
import type { Recipe, RecipeFilter } from '@/types'

/**
 * 菜谱状态管理
 */
export const useRecipeStore = defineStore('recipe', () => {
  // 状态
  const recipes = ref<Recipe[]>([])
  const selectedRecipe = ref<Recipe | null>(null)
  const favorites = ref<number[]>([])
  const filter = ref<RecipeFilter>({
    searchTerm: '',
    category: 'all'
  })
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const categories = computed(() => getRecipeCategories())
  
  const filteredRecipes = computed(() => {
    let result = recipes.value

    // 分类筛选
    if (filter.value.category && filter.value.category !== 'all') {
      result = result.filter(recipe => recipe.category === filter.value.category)
    }

    // 搜索筛选
    if (filter.value.searchTerm) {
      const searchTerm = filter.value.searchTerm.toLowerCase()
      result = result.filter(recipe => 
        recipe.title.toLowerCase().includes(searchTerm) ||
        recipe.description.toLowerCase().includes(searchTerm) ||
        recipe.ingredients.some(ingredient => ingredient.toLowerCase().includes(searchTerm))
      )
    }

    // 难度筛选
    if (filter.value.difficulty) {
      result = result.filter(recipe => recipe.difficulty === filter.value.difficulty)
    }

    // 烹饪时间筛选
    if (filter.value.cookTimeRange) {
      const { min, max } = filter.value.cookTimeRange
      result = result.filter(recipe => recipe.cookTime >= min && recipe.cookTime <= max)
    }

    return result
  })

  const favoriteRecipes = computed(() => {
    return recipes.value.filter(recipe => favorites.value.includes(recipe.id))
  })

  const recipeStats = computed(() => ({
    total: recipes.value.length,
    favorites: favorites.value.length,
    categories: categories.value.length - 1, // 排除"全部"
    avgCookTime: Math.round(
      recipes.value.reduce((sum, recipe) => sum + recipe.cookTime, 0) / recipes.value.length
    )
  }))

  // 动作
  const loadRecipes = async () => {
    try {
      loading.value = true
      error.value = null
      
      // 模拟异步加载
      await new Promise(resolve => setTimeout(resolve, 500))
      
      recipes.value = mockRecipes
    } catch (err) {
      error.value = '加载菜谱失败'
      console.error('Failed to load recipes:', err)
    } finally {
      loading.value = false
    }
  }

  const selectRecipe = (recipe: Recipe | null) => {
    selectedRecipe.value = recipe
  }

  const selectRecipeById = (id: number) => {
    const recipe = getRecipeById(id)
    if (recipe) {
      selectedRecipe.value = recipe
    }
  }

  const updateFilter = (newFilter: Partial<RecipeFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
  }

  const clearFilter = () => {
    filter.value = {
      searchTerm: '',
      category: 'all'
    }
  }

  const toggleFavorite = (recipeId: number) => {
    const index = favorites.value.indexOf(recipeId)
    if (index > -1) {
      favorites.value.splice(index, 1)
    } else {
      favorites.value.push(recipeId)
    }
    
    // 保存到本地存储
    localStorage.setItem('recipe-favorites', JSON.stringify(favorites.value))
  }

  const isFavorite = (recipeId: number) => {
    return favorites.value.includes(recipeId)
  }

  const loadFavorites = () => {
    try {
      const saved = localStorage.getItem('recipe-favorites')
      if (saved) {
        favorites.value = JSON.parse(saved)
      }
    } catch (err) {
      console.error('Failed to load favorites:', err)
    }
  }

  const searchRecipesByTerm = (searchTerm: string) => {
    return searchRecipes(searchTerm)
  }

  const getRecipesByCategory = (category: string) => {
    if (category === 'all') {
      return recipes.value
    }
    return recipes.value.filter(recipe => recipe.category === category)
  }

  // 初始化
  const initialize = async () => {
    loadFavorites()
    await loadRecipes()
  }

  return {
    // 状态
    recipes,
    selectedRecipe,
    favorites,
    filter,
    loading,
    error,
    
    // 计算属性
    categories,
    filteredRecipes,
    favoriteRecipes,
    recipeStats,
    
    // 动作
    loadRecipes,
    selectRecipe,
    selectRecipeById,
    updateFilter,
    clearFilter,
    toggleFavorite,
    isFavorite,
    loadFavorites,
    searchRecipesByTerm,
    getRecipesByCategory,
    initialize
  }
})
