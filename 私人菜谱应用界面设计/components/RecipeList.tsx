import { RecipeCard } from "./RecipeCard";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Search, Leaf } from "lucide-react";
import { useState } from "react";

interface Recipe {
  id: number;
  title: string;
  description: string;
  image: string;
  cookTime: number;
  servings: number;
  difficulty: string;
  category: string;
  ingredients: string[];
  steps: string[];
}

interface RecipeListProps {
  recipes: Recipe[];
  onRecipeSelect: (recipe: Recipe) => void;
}

export function RecipeList({ recipes, onRecipeSelect }: RecipeListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = ["all", ...Array.from(new Set(recipes.map(r => r.category)))];

  const filteredRecipes = recipes.filter(recipe => {
    const matchesSearch = recipe.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         recipe.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || recipe.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-8">
      <div className="text-center space-y-4 py-8 fresh-gradient rounded-2xl">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-white rounded-full shadow-lg">
            <Leaf className="w-8 h-8 text-emerald-600" />
          </div>
          <h1 className="text-emerald-800">我的菜谱</h1>
        </div>
        <p className="text-emerald-700 max-w-md mx-auto">
          收藏你喜欢的美食，随时查看制作方法，享受烹饪的乐趣
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 p-6 bg-white rounded-xl fresh-shadow border border-emerald-100">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-500 w-4 h-4" />
          <Input
            type="text"
            placeholder="搜索菜谱..."
            className="pl-10 border-emerald-200 focus:border-emerald-400 focus:ring-emerald-200 bg-emerald-50/50"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full sm:w-48 border-emerald-200 focus:border-emerald-400 focus:ring-emerald-200 bg-emerald-50/50">
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent className="border-emerald-200">
            <SelectItem value="all" className="focus:bg-emerald-50 focus:text-emerald-700">所有分类</SelectItem>
            {categories.slice(1).map(category => (
              <SelectItem key={category} value={category} className="focus:bg-emerald-50 focus:text-emerald-700">
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredRecipes.map(recipe => (
          <RecipeCard
            key={recipe.id}
            recipe={recipe}
            onClick={() => onRecipeSelect(recipe)}
          />
        ))}
      </div>

      {filteredRecipes.length === 0 && (
        <div className="text-center py-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-emerald-50 rounded-full mb-4">
            <Search className="w-6 h-6 text-emerald-400" />
          </div>
          <p className="text-emerald-600">没有找到匹配的菜谱</p>
          <p className="text-sm text-emerald-500 mt-1">试试其他关键词或分类</p>
        </div>
      )}
    </div>
  );
}