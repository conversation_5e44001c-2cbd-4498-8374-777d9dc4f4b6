import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";
import { ArrowLeft, Clock, Users, ChefHat } from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";

interface Recipe {
  id: number;
  title: string;
  description: string;
  image: string;
  cookTime: number;
  servings: number;
  difficulty: string;
  category: string;
  ingredients: string[];
  steps: string[];
}

interface RecipeDetailProps {
  recipe: Recipe;
  onBack: () => void;
}

export function RecipeDetail({ recipe, onBack }: RecipeDetailProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "简单": return "bg-emerald-50 text-emerald-700 border-emerald-200";
      case "中等": return "bg-amber-50 text-amber-700 border-amber-200";
      case "困难": return "bg-orange-50 text-orange-700 border-orange-200";
      default: return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={onBack} className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50">
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回列表
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="aspect-video rounded-lg overflow-hidden fresh-shadow">
            <ImageWithFallback
              src={recipe.image}
              alt={recipe.title}
              className="w-full h-full object-cover"
            />
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <h1 className="text-gray-800">{recipe.title}</h1>
              <p className="text-muted-foreground">{recipe.description}</p>
            </div>

            <div className="flex flex-wrap gap-2">
              <Badge className={`${getDifficultyColor(recipe.difficulty)} border shadow-sm`}>
                <ChefHat className="w-3 h-3 mr-1" />
                {recipe.difficulty}
              </Badge>
              <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 border">
                <Clock className="w-3 h-3 mr-1" />
                {recipe.cookTime}分钟
              </Badge>
              <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 border">
                <Users className="w-3 h-3 mr-1" />
                {recipe.servings}人份
              </Badge>
              <Badge variant="outline" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50">
                {recipe.category}
              </Badge>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <Card className="fresh-card border-0 fresh-shadow">
            <CardHeader className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-t-lg">
              <CardTitle className="text-emerald-800 flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                所需材料
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <ul className="space-y-3">
                {recipe.ingredients.map((ingredient, index) => (
                  <li key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-emerald-50 transition-colors">
                    <div className="w-2 h-2 bg-emerald-400 rounded-full flex-shrink-0"></div>
                    <span className="text-gray-700">{ingredient}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="fresh-card border-0 fresh-shadow">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-sky-50 rounded-t-lg">
              <CardTitle className="text-blue-800 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                制作步骤
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {recipe.steps.map((step, index) => (
                  <div key={index} className="flex gap-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-emerald-500 to-green-500 text-white rounded-full flex items-center justify-center text-sm shadow-sm">
                      {index + 1}
                    </div>
                    <div className="flex-1 pt-1">
                      <p className="text-gray-700 leading-relaxed">{step}</p>
                      {index < recipe.steps.length - 1 && (
                        <Separator className="mt-4 bg-emerald-100" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}