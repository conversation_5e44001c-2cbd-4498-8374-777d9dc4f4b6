import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Clock, Users } from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";

interface RecipeCardProps {
  recipe: {
    id: number;
    title: string;
    description: string;
    image: string;
    cookTime: number;
    servings: number;
    difficulty: string;
    category: string;
  };
  onClick: () => void;
}

export function RecipeCard({ recipe, onClick }: RecipeCardProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "简单": return "bg-emerald-50 text-emerald-700 border-emerald-200";
      case "中等": return "bg-amber-50 text-amber-700 border-amber-200";
      case "困难": return "bg-orange-50 text-orange-700 border-orange-200";
      default: return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  return (
    <Card 
      className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 overflow-hidden fresh-card fresh-shadow border-0"
      onClick={onClick}
    >
      <div className="aspect-video relative overflow-hidden">
        <ImageWithFallback
          src={recipe.image}
          alt={recipe.title}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
        />
        <div className="absolute top-3 right-3">
          <Badge className={`${getDifficultyColor(recipe.difficulty)} shadow-sm border`}>
            {recipe.difficulty}
          </Badge>
        </div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
      <CardContent className="p-5">
        <div className="space-y-3">
          <h3 className="line-clamp-2 text-gray-800">{recipe.title}</h3>
          <p className="text-muted-foreground text-sm line-clamp-2">
            {recipe.description}
          </p>
          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-emerald-600" />
                <span>{recipe.cookTime}分钟</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4 text-emerald-600" />
                <span>{recipe.servings}人份</span>
              </div>
            </div>
            <Badge variant="secondary" className="bg-emerald-50 text-emerald-700 border-emerald-200 border">
              {recipe.category}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}