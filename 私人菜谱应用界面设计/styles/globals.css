@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 14px;
  --background: #f8fffe;
  --foreground: oklch(0.145 0 0);
  --card: #ffffff;
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #4caf50;
  --primary-foreground: #ffffff;
  --secondary: #e8f5e8;
  --secondary-foreground: #2e7d32;
  --muted: #f1f8f1;
  --muted-foreground: #5d7f5f;
  --accent: #c8e6c9;
  --accent-foreground: #1b5e20;
  --destructive: #ff7043;
  --destructive-foreground: #ffffff;
  --border: rgba(76, 175, 80, 0.2);
  --input: transparent;
  --input-background: #f8fffe;
  --switch-background: #c8e6c9;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: #4caf50;
  --chart-1: #4caf50;
  --chart-2: #81c784;
  --chart-3: #29b6f6;
  --chart-4: #ffd600;
  --chart-5: #ff7043;
  --radius: 0.625rem;
  --sidebar: #f8fffe;
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: #4caf50;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #e8f5e8;
  --sidebar-accent-foreground: #2e7d32;
  --sidebar-border: rgba(76, 175, 80, 0.2);
  --sidebar-ring: #4caf50;
  
  /* 清新主题专用颜色 */
  --fresh-green: #4caf50;
  --fresh-light-green: #81c784;
  --fresh-yellow: #ffd600;
  --fresh-orange: #ff7043;
  --fresh-blue: #29b6f6;
  --fresh-bg: #f8fffe;
  --fresh-card: #ffffff;
  --fresh-border: rgba(76, 175, 80, 0.15);
}

.dark {
  --background: #0d1f0f;
  --foreground: #e8f5e8;
  --card: #1a2e1c;
  --card-foreground: #e8f5e8;
  --popover: #1a2e1c;
  --popover-foreground: #e8f5e8;
  --primary: #81c784;
  --primary-foreground: #0d1f0f;
  --secondary: #2e7d32;
  --secondary-foreground: #c8e6c9;
  --muted: #1e3a21;
  --muted-foreground: #81c784;
  --accent: #2e7d32;
  --accent-foreground: #c8e6c9;
  --destructive: #ff7043;
  --destructive-foreground: #ffffff;
  --border: rgba(129, 199, 132, 0.2);
  --input: rgba(129, 199, 132, 0.1);
  --ring: #81c784;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: #81c784;
  --chart-2: #4caf50;
  --chart-3: #29b6f6;
  --chart-4: #ffd600;
  --chart-5: #ff7043;
  --sidebar: #0d1f0f;
  --sidebar-foreground: #e8f5e8;
  --sidebar-primary: #81c784;
  --sidebar-primary-foreground: #0d1f0f;
  --sidebar-accent: #2e7d32;
  --sidebar-accent-foreground: #c8e6c9;
  --sidebar-border: rgba(129, 199, 132, 0.2);
  --sidebar-ring: #81c784;
  
  /* 暗色模式清新主题 */
  --fresh-green: #81c784;
  --fresh-light-green: #a5d6a7;
  --fresh-yellow: #fff176;
  --fresh-orange: #ffab91;
  --fresh-blue: #64b5f6;
  --fresh-bg: #0d1f0f;
  --fresh-card: #1a2e1c;
  --fresh-border: rgba(129, 199, 132, 0.2);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  
  /* 清新主题颜色 */
  --color-fresh-green: var(--fresh-green);
  --color-fresh-light-green: var(--fresh-light-green);
  --color-fresh-yellow: var(--fresh-yellow);
  --color-fresh-orange: var(--fresh-orange);
  --color-fresh-blue: var(--fresh-blue);
  --color-fresh-bg: var(--fresh-bg);
  --color-fresh-card: var(--fresh-card);
  --color-fresh-border: var(--fresh-border);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--fresh-bg);
  }
}

/**
 * Base typography. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

html {
  font-size: var(--font-size);
}

/* 清新主题的自定义类 */
@layer utilities {
  .fresh-card {
    background: var(--fresh-card);
    border: 1px solid var(--fresh-border);
  }
  
  .fresh-gradient {
    background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
  }
  
  .fresh-shadow {
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.1);
  }
}